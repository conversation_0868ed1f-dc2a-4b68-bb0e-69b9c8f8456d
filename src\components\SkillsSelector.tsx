import React, { useState, useEffect } from 'react';
import { X, Plus, Search } from 'lucide-react';
import { skillService } from '../services/skillService';
import { Skill } from '../types/skill';
import CompetencyPill from './CompetencyPill';

interface SkillsSelectorProps {
  selectedSkillIds: string[];
  onSkillsChange: (skillIds: string[]) => void;
  className?: string;
}

const SkillsSelector: React.FC<SkillsSelectorProps> = ({
  selectedSkillIds,
  onSkillsChange,
  className = ''
}) => {
  const [availableSkills, setAvailableSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSkills();
  }, []);

  const loadSkills = async () => {
    try {
      setLoading(true);
      setError(null);
      const skills = await skillService.getAllSkills();
      setAvailableSkills(skills);
    } catch (err) {
      setError('Failed to load skills');
      console.error('Error loading skills:', err);
    } finally {
      setLoading(false);
    }
  };

  const selectedSkills = availableSkills.filter(skill => 
    selectedSkillIds.includes(skill.id!)
  );

  const unselectedSkills = availableSkills.filter(skill => 
    !selectedSkillIds.includes(skill.id!) &&
    skill.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSkillSelect = (skillId: string) => {
    if (!selectedSkillIds.includes(skillId)) {
      onSkillsChange([...selectedSkillIds, skillId]);
    }
    setSearchTerm('');
    setShowDropdown(false);
  };

  const handleSkillRemove = (skillId: string) => {
    onSkillsChange(selectedSkillIds.filter(id => id !== skillId));
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Selected Skills */}
      {selectedSkills.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Selected Skills ({selectedSkills.length})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedSkills.map((skill) => (
              <div
                key={skill.id}
                className="inline-flex items-center bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm border border-blue-200"
              >
                <span className="mr-2">{skill.name}</span>
                <CompetencyPill competencyModel={skill.competencyModel} className="mr-2 text-xs" />
                <button
                  type="button"
                  onClick={() => handleSkillRemove(skill.id!)}
                  className="text-blue-500 hover:text-blue-700 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Skills Section */}
      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Add Skills
        </label>
        
        {error && (
          <div className="mb-2 text-sm text-red-600">
            {error}
          </div>
        )}

        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search and select skills..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setShowDropdown(true)}
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={loading}
          />
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Plus className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Dropdown */}
        {showDropdown && !loading && (
          <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {unselectedSkills.length === 0 ? (
              <div className="px-4 py-3 text-sm text-gray-500">
                {searchTerm ? 'No skills match your search' : 'All skills are already selected'}
              </div>
            ) : (
              unselectedSkills.map((skill) => (
                <button
                  key={skill.id}
                  type="button"
                  onClick={() => handleSkillSelect(skill.id!)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{skill.name}</div>
                      <div className="text-sm text-gray-500 truncate">{skill.description}</div>
                    </div>
                    <CompetencyPill competencyModel={skill.competencyModel} className="ml-2" />
                  </div>
                </button>
              ))
            )}
          </div>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default SkillsSelector;
