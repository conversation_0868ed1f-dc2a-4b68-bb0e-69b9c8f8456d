import { db } from '../config/firebase';
import { collection, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, query, where } from 'firebase/firestore';
import { Certification } from '../types/role';

const certificationsCollection = collection(db, 'certifications');

export const certificationService = {
  async getAllCertifications(): Promise<Certification[]> {
    const snapshot = await getDocs(certificationsCollection);
    return snapshot.docs.map(doc => ({ ...doc.data(), id: doc.id })) as Certification[];
  },

  async getCertificationById(id: string): Promise<Certification | null> {
    const docRef = doc(db, 'certifications', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { ...docSnap.data(), id: docSnap.id } as Certification : null;
  },

  async createCertification(certificationData: Omit<Certification, 'id'>): Promise<Certification> {
    const docRef = await addDoc(certificationsCollection, certificationData);
    return { ...certificationData, id: docRef.id };
  },

  async updateCertification(id: string, updates: Partial<Certification>): Promise<void> {
    const docRef = doc(db, 'certifications', id);
    await updateDoc(docRef, updates);
  },

  async deleteCertification(id: string): Promise<void> {
    const docRef = doc(db, 'certifications', id);
    await deleteDoc(docRef);
  },

  async getCertificationsForSkill(skillId: string): Promise<Certification[]> {
    const q = query(certificationsCollection, where('skillId', '==', skillId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ ...doc.data(), id: doc.id })) as Certification[];
  }
};