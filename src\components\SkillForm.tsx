import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import { skillService } from '../services/skillService';
import { Skill, CompetencyModel, COMPETENCY_MODELS, COMPETENCY_MODEL_COLORS } from '../types/skill';

const SkillForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    competencyModel: 'Primary' as CompetencyModel
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(isEditing);

  useEffect(() => {
    if (isEditing && id) {
      loadSkill(id);
    }
  }, [isEditing, id]);

  const loadSkill = async (skillId: string) => {
    try {
      setInitialLoading(true);
      const skill = await skillService.getSkillById(skillId);
      if (skill) {
        setFormData({
          name: skill.name,
          description: skill.description,
          competencyModel: skill.competencyModel
        });
      } else {
        setError('Skill not found');
      }
    } catch (err) {
      setError('Failed to load skill');
      console.error('Error loading skill:', err);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.description.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (isEditing && id) {
        await skillService.updateSkill(id, formData);
      } else {
        await skillService.createSkill(formData);
      }

      navigate('/skills');
    } catch (err) {
      setError(isEditing ? 'Failed to update skill' : 'Failed to create skill');
      console.error('Error saving skill:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCompetencyModelChange = (model: CompetencyModel) => {
    setFormData(prev => ({
      ...prev,
      competencyModel: model
    }));
  };

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/skills')}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Skills
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {isEditing ? 'Edit Skill' : 'Create New Skill'}
        </h1>
        <p className="mt-2 text-gray-600">
          {isEditing ? 'Update the skill information below' : 'Add a new skill to your competency model'}
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white shadow-sm rounded-lg border border-gray-200 p-6 space-y-6">
        {/* Competency Model Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Skill Competency Model <span className="text-red-500">*</span>
          </label>
          <div className="flex flex-wrap gap-3">
            {COMPETENCY_MODELS.map((model) => {
              const isSelected = formData.competencyModel === model;
              const colorClass = COMPETENCY_MODEL_COLORS[model];
              
              return (
                <button
                  key={model}
                  type="button"
                  onClick={() => handleCompetencyModelChange(model)}
                  className={`px-4 py-2 rounded-full text-sm font-medium border transition-all ${
                    isSelected
                      ? `${colorClass} ring-2 ring-blue-500 ring-offset-2`
                      : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {model}
                </button>
              );
            })}
          </div>
        </div>

        {/* Skill Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Skill Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Enter skill name"
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>

        {/* Skill Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Skill Description <span className="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter skill description"
            rows={4}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate('/skills')}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {loading ? 'Saving...' : (isEditing ? 'Update Skill' : 'Create Skill')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SkillForm;
