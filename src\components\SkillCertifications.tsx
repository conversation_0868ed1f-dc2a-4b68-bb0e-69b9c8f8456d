import React, { useState } from 'react';
import { Plus, Edit2, Trash2, ExternalLink } from 'lucide-react';
import { Certification } from '../types/role';

interface SkillCertificationsProps {
  skillId: string;
  skillName: string;
  certifications: Certification[];
  onCertificationsChange: (certifications: Certification[]) => void;
}

const SkillCertifications: React.FC<SkillCertificationsProps> = ({
  skillId,
  skillName,
  certifications,
  onCertificationsChange
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    links: ['']
  });

  console.log(`SkillCertifications (${skillName}): Rendered with ${certifications.length} certifications`);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      links: ['']
    });
    setEditingIndex(null);
    setShowAddForm(false);
  };

  const handleAddCertification = () => {
    console.log(`SkillCertifications (${skillName}): Adding certification`, formData);

    if (!formData.name.trim() || !formData.description.trim()) {
      alert('Please fill in both name and description');
      return;
    }

    const validLinks = formData.links.filter(link => link.trim() !== '');
    const newCertification: Certification = {
      id: Date.now().toString(),
      name: formData.name.trim(),
      description: formData.description.trim(),
      links: validLinks
    };

    let updatedCertifications: Certification[];
    if (editingIndex !== null) {
      updatedCertifications = [...certifications];
      updatedCertifications[editingIndex] = newCertification;
      console.log(`SkillCertifications (${skillName}): Updated certification at index ${editingIndex}`);
    } else {
      updatedCertifications = [...certifications, newCertification];
      console.log(`SkillCertifications (${skillName}): Added new certification`);
    }

    console.log(`SkillCertifications (${skillName}): Calling onCertificationsChange with:`, updatedCertifications);
    onCertificationsChange(updatedCertifications);
    resetForm();
  };

  const handleEdit = (index: number) => {
    const cert = certifications[index];
    setFormData({
      name: cert.name,
      description: cert.description,
      links: cert.links.length > 0 ? cert.links : ['']
    });
    setEditingIndex(index);
    setShowAddForm(true);
  };

  const handleDelete = (index: number) => {
    const updatedCertifications = certifications.filter((_, i) => i !== index);
    console.log(`SkillCertifications (${skillName}): Deleted certification at index ${index}`);
    onCertificationsChange(updatedCertifications);
  };

  const addLinkField = () => {
    setFormData(prev => ({
      ...prev,
      links: [...prev.links, '']
    }));
  };

  const updateLink = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      links: prev.links.map((link, i) => i === index ? value : link)
    }));
  };

  const removeLinkField = (index: number) => {
    setFormData(prev => ({
      ...prev,
      links: prev.links.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h5 className="text-md font-medium text-gray-900">
          Certifications ({certifications.length})
        </h5>
        <button
          type="button"
          onClick={() => setShowAddForm(true)}
          className="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Certification
        </button>
      </div>

      {/* Existing Certifications */}
      {certifications.length > 0 && (
        <div className="space-y-3">
          {certifications.map((cert, index) => (
            <div key={cert.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h6 className="font-medium text-gray-900">{cert.name}</h6>
                  <p className="text-sm text-gray-600 mt-1">{cert.description}</p>
                  {cert.links.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {cert.links.map((link, linkIndex) => (
                        <a
                          key={linkIndex}
                          href={link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Link {linkIndex + 1}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    type="button"
                    onClick={() => handleEdit(index)}
                    className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                  >
                    <Edit2 className="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDelete(index)}
                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h6 className="font-medium text-gray-900 mb-4">
            {editingIndex !== null ? 'Edit' : 'Add'} Certification
          </h6>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Certification Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., AWS Certified Solutions Architect"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Brief description of the certification..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Links (Optional)
              </label>
              {formData.links.map((link, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="url"
                    value={link}
                    onChange={(e) => updateLink(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://..."
                  />
                  {formData.links.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeLinkField(index)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addLinkField}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                + Add another link
              </button>
            </div>

            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={(e) => {
                  console.log('🔥 SUBMIT BUTTON CLICKED!', { skillName, formData });
                  alert(`Submit button clicked for ${skillName}! Form data: ${JSON.stringify(formData)}`);
                  handleAddCertification();
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
              >
                {editingIndex !== null ? 'Update' : 'Add'} Certification
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SkillCertifications;
