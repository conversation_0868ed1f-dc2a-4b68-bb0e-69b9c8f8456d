import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, Users } from 'lucide-react';
import { roleService } from '../services/roleService';
import { skillService } from '../services/skillService';
import { Role, RoleSkill, ProficiencyLevel, Certification } from '../types/role';
import { Skill } from '../types/skill';
import SkillsSelector from './SkillsSelector';
import ProficiencySelector from './ProficiencySelector';
import CertificationsManager from './CertificationsManager';

const RoleForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    skills: [] as RoleSkill[]
  });
  const [selectedSkillIds, setSelectedSkillIds] = useState<string[]>([]);
  const [availableSkills, setAvailableSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(isEditing);

  useEffect(() => {
    loadSkills();
    if (isEditing && id) {
      loadRole(id);
    }
  }, [isEditing, id]);

  const loadSkills = async () => {
    try {
      const skills = await skillService.getAllSkills();
      setAvailableSkills(skills);
    } catch (err) {
      console.error('Error loading skills:', err);
    }
  };

  const loadRole = async (roleId: string) => {
    try {
      setInitialLoading(true);
      const role = await roleService.getRoleById(roleId);
      if (role) {
        console.log('RoleForm: Loaded role from database', role);
        console.log('RoleForm: Skills with certifications', role.skills?.map(s => ({
          skillId: s.skillId,
          skillName: s.skillName,
          certifications: s.certifications
        })));
        
        // Ensure certifications is always an array
        const skillsWithCertifications = (role.skills || []).map(skill => ({
          ...skill,
          certifications: skill.certifications || []
        }));
        
        setFormData({
          name: role.name,
          description: role.description,
          skills: skillsWithCertifications
        });
        setSelectedSkillIds(skillsWithCertifications.map(s => s.skillId));
      } else {
        setError('Role not found');
      }
    } catch (err) {
      setError('Failed to load role');
      console.error('Error loading role:', err);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.description.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    if (formData.skills.length === 0) {
      setError('Please add at least one skill');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Ensure all skills have valid certifications arrays
      console.log('RoleForm: Processing skills before save. Original formData.skills:', formData.skills);

      const processedSkills = formData.skills.map((skill, index) => {
        console.log(`RoleForm: Processing skill ${index + 1} (${skill.skillName}):`, {
          originalSkill: skill,
          certificationsIsArray: Array.isArray(skill.certifications),
          certificationsLength: skill.certifications?.length,
          certificationsValue: skill.certifications
        });

        const processedSkill = {
          ...skill,
          certifications: Array.isArray(skill.certifications) ? skill.certifications : []
        };

        console.log(`RoleForm: Processed skill ${index + 1}:`, processedSkill);
        return processedSkill;
      });

      console.log('RoleForm: All processed skills:', processedSkills);

      const roleData: Omit<Role, 'id'> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        skills: processedSkills
      };

      console.log('RoleForm: Saving role data', roleData);
      console.log('RoleForm: Skills with certifications', roleData.skills.map(s => ({
        skillId: s.skillId,
        skillName: s.skillName,
        certificationsCount: s.certifications?.length || 0,
        certifications: s.certifications
      })));

      // Additional detailed logging for debugging
      roleData.skills.forEach((skill, index) => {
        console.log(`RoleForm: Skill ${index + 1} (${skill.skillName}):`, {
          skillId: skill.skillId,
          proficiency: skill.proficiency,
          certificationsArray: skill.certifications,
          certificationsLength: skill.certifications?.length,
          certificationsType: typeof skill.certifications,
          isArray: Array.isArray(skill.certifications)
        });
      });

      if (isEditing && id) {
        await roleService.updateRole(id, roleData);
      } else {
        await roleService.createRole(roleData);
      }

      navigate('/roles');
    } catch (err) {
      setError(isEditing ? 'Failed to update role' : 'Failed to create role');
      console.error('Error saving role:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSkillsChange = (skillIds: string[]) => {
    console.log('RoleForm: handleSkillsChange called with skillIds:', skillIds);
    console.log('RoleForm: Current formData.skills:', formData.skills);

    setSelectedSkillIds(skillIds);

    // Use functional state update to ensure we have the latest state
    setFormData(prev => {
      console.log('RoleForm: handleSkillsChange - prev.skills:', prev.skills);

      // Update formData.skills to match selected skills
      const updatedSkills = skillIds.map(skillId => {
        const existingSkill = prev.skills.find(s => s.skillId === skillId);
        const skill = availableSkills.find(s => s.id === skillId);

        console.log(`RoleForm: Processing skill ${skillId}:`, {
          existingSkill,
          skillName: skill?.name,
          willUseExisting: !!existingSkill,
          existingCertifications: existingSkill?.certifications
        });

        if (existingSkill) {
          console.log(`RoleForm: Using existing skill with ${existingSkill.certifications?.length || 0} certifications`);
          return {
            ...existingSkill,
            certifications: Array.isArray(existingSkill.certifications) ? [...existingSkill.certifications] : []
          };
        } else {
          console.log(`RoleForm: Creating new skill with empty certifications`);
          return {
            skillId,
            skillName: skill?.name || '',
            proficiency: 'Beginner' as ProficiencyLevel,
            certifications: []
          };
        }
      });

      console.log('RoleForm: Updated skills after handleSkillsChange:', updatedSkills);

      return {
        ...prev,
        skills: updatedSkills
      };
    });
  };

  const updateSkillProficiency = (skillId: string, proficiency: ProficiencyLevel) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.map(skill =>
        skill.skillId === skillId ? { ...skill, proficiency } : skill
      )
    }));
  };

  const updateSkillCertifications = (skillId: string, certifications: Certification[]) => {
    console.log('RoleForm: updateSkillCertifications called with:', { skillId, certifications });
    console.log('RoleForm: Current formData.skills before update:', formData.skills);

    setFormData(prev => {
      const updatedFormData = {
        ...prev,
        skills: prev.skills.map(skill => {
          if (skill.skillId === skillId) {
            console.log('RoleForm: Updating skill', skill.skillName, 'with certifications:', certifications);
            return {
              ...skill,
              certifications: Array.isArray(certifications) ? [...certifications] : []
            };
          }
          return skill;
        })
      };
      console.log('RoleForm: Updated form data after certification update:', updatedFormData);
      console.log('RoleForm: Specific skill certifications:', updatedFormData.skills.find(s => s.skillId === skillId)?.certifications);
      return updatedFormData;
    });
  };

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/roles')}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Roles
        </button>
        <div className="flex items-center">
          <Users className="h-8 w-8 text-blue-600 mr-3" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? 'Edit Role' : 'Create New Role'}
            </h1>
            <p className="mt-2 text-gray-600">
              {isEditing ? 'Update the role information below' : 'Define a new role with required skills and proficiencies'}
            </p>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6 space-y-6">
          <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Senior Software Engineer"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role Description <span className="text-red-500">*</span>
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the role responsibilities and requirements..."
              required
            />
          </div>
        </div>

        {/* Skills Selection */}
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Skills & Proficiencies</h2>
          
          <SkillsSelector
            selectedSkillIds={selectedSkillIds}
            onSkillsChange={handleSkillsChange}
            className="mb-8"
          />

          {/* Selected Skills with Proficiency and Certifications */}
          {formData.skills.length > 0 && (
            <div className="space-y-6">
              <h3 className="text-md font-medium text-gray-900">Configure Skills</h3>
              {formData.skills.map((roleSkill, index) => (
                <div key={`${roleSkill.skillId}-${index}`} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">{roleSkill.skillName}</h4>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <ProficiencySelector
                        selectedProficiency={roleSkill.proficiency}
                        onProficiencyChange={(proficiency) => updateSkillProficiency(roleSkill.skillId, proficiency)}
                      />
                    </div>
                    
                    <div>
                      <CertificationsManager
                        certifications={roleSkill.certifications || []}
                        onCertificationsChange={(certifications) => {
                          console.log('RoleForm: CertificationsManager callback called with:', certifications);
                          console.log('RoleForm: Skill ID:', roleSkill.skillId);
                          console.log('RoleForm: Skill Name:', roleSkill.skillName);
                          console.log('RoleForm: Current skill certifications before update:', roleSkill.certifications);
                          updateSkillCertifications(roleSkill.skillId, certifications);
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Debug Button (temporary) */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <button
            type="button"
            onClick={() => {
              console.log('=== DEBUG: Current Form State ===');
              console.log('formData:', formData);
              console.log('selectedSkillIds:', selectedSkillIds);
              console.log('Skills with certifications:');
              formData.skills.forEach((skill, index) => {
                console.log(`  Skill ${index + 1}: ${skill.skillName}`, {
                  skillId: skill.skillId,
                  proficiency: skill.proficiency,
                  certifications: skill.certifications,
                  certificationsCount: skill.certifications?.length || 0
                });
              });
              console.log('=== END DEBUG ===');
            }}
            className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
          >
            Debug: Log Current State
          </button>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Saving...' : (isEditing ? 'Update Role' : 'Create Role')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RoleForm;