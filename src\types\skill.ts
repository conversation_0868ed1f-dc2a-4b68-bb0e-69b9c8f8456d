export type CompetencyModel = 'Primary' | 'Secondary' | 'Cloud/AI' | 'Consulting';

export interface Skill {
  id?: string;
  name: string;
  description: string;
  competencyModel: CompetencyModel;
  createdAt?: Date;
  updatedAt?: Date;
}

export const COMPETENCY_MODELS: CompetencyModel[] = [
  'Primary',
  'Secondary', 
  'Cloud/AI',
  'Consulting'
];

export const COMPETENCY_MODEL_COLORS = {
  'Primary': 'bg-blue-100 text-blue-800 border-blue-200',
  'Secondary': 'bg-green-100 text-green-800 border-green-200',
  'Cloud/AI': 'bg-purple-100 text-purple-800 border-purple-200',
  'Consulting': 'bg-orange-100 text-orange-800 border-orange-200'
} as const;
