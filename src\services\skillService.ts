import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  getDoc,
  orderBy,
  query,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Skill } from '../types/skill';

const COLLECTION_NAME = 'skills';

export const skillService = {
  // Create a new skill
  async createSkill(skill: Omit<Skill, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const skillData = {
        ...skill,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };
      
      const docRef = await addDoc(collection(db, COLLECTION_NAME), skillData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating skill:', error);
      throw new Error('Failed to create skill');
    }
  },

  // Get all skills
  async getAllSkills(): Promise<Skill[]> {
    try {
      const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Skill[];
    } catch (error) {
      console.error('Error fetching skills:', error);
      throw new Error('Failed to fetch skills');
    }
  },

  // Get a single skill by ID
  async getSkillById(id: string): Promise<Skill | null> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate()
        } as Skill;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching skill:', error);
      throw new Error('Failed to fetch skill');
    }
  },

  // Update a skill
  async updateSkill(id: string, updates: Partial<Omit<Skill, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const updateData = {
        ...updates,
        updatedAt: Timestamp.now()
      };
      
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating skill:', error);
      throw new Error('Failed to update skill');
    }
  },

  // Delete a skill
  async deleteSkill(id: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting skill:', error);
      throw new Error('Failed to delete skill');
    }
  }
};
