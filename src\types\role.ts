export type ProficiencyLevel = 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';

export interface Certification {
  id?: string;
  name: string;
  description: string;
  links: string[];
}

export interface RoleSkill {
  skillId: string;
  skillName: string;
  proficiency: ProficiencyLevel;
  certifications: Certification[];
}

export interface Role {
  id?: string;
  name: string;
  description: string;
  skills: RoleSkill[];
  createdAt?: Date;
  updatedAt?: Date;
}

export const PROFICIENCY_LEVELS: ProficiencyLevel[] = [
  'Beginner',
  'Intermediate',
  'Advanced',
  'Expert'
];

export const PROFICIENCY_COLORS = {
  'Beginner': 'bg-green-100 text-green-800 border-green-200',
  'Intermediate': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'Advanced': 'bg-orange-100 text-orange-800 border-orange-200',
  'Expert': 'bg-red-100 text-red-800 border-red-200'
} as const;
