import React from 'react';
import { CompetencyModel, COMPETENCY_MODEL_COLORS } from '../types/skill';

interface CompetencyPillProps {
  competencyModel: CompetencyModel;
  className?: string;
}

const CompetencyPill: React.FC<CompetencyPillProps> = ({ competencyModel, className = '' }) => {
  const colorClass = COMPETENCY_MODEL_COLORS[competencyModel];

  return (
    <span
      className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${colorClass} ${className}`}
    >
      {competencyModel}
    </span>
  );
};

export default CompetencyPill;
