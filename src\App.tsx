import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import SkillsList from './pages/SkillsList';
import SkillForm from './components/SkillForm';
import RolesList from './pages/RolesList';
import RoleForm from './components/RoleForm';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/roles" />} />
          <Route path="/roles" element={<RolesList />} />
          <Route path="/roles/new" element={<RoleForm />} />
          <Route path="/roles/create" element={<RoleForm />} />
          <Route path="/roles/edit/:id" element={<RoleForm />} />
          <Route path="/skills" element={<SkillsList />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;