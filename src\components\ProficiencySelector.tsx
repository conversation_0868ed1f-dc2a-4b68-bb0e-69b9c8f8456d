import React from 'react';
import { ProficiencyLevel, PROFICIENCY_LEVELS, PROFICIENCY_COLORS } from '../types/role';

interface ProficiencySelectorProps {
  selectedProficiency: ProficiencyLevel;
  onProficiencyChange: (proficiency: ProficiencyLevel) => void;
  className?: string;
}

const ProficiencySelector: React.FC<ProficiencySelectorProps> = ({
  selectedProficiency,
  onProficiencyChange,
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        Proficiency Level <span className="text-red-500">*</span>
      </label>
      <div className="flex flex-wrap gap-3">
        {PROFICIENCY_LEVELS.map((level) => {
          const isSelected = selectedProficiency === level;
          const colorClass = PROFICIENCY_COLORS[level];
          
          return (
            <button
              key={level}
              type="button"
              onClick={() => onProficiencyChange(level)}
              className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border transition-all ${
                isSelected
                  ? `${colorClass} ring-2 ring-offset-2 ring-blue-500`
                  : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
              }`}
            >
              {level}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default ProficiencySelector;
