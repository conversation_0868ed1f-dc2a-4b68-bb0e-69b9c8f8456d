import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  getDoc,
  orderBy,
  query,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Role } from '../types/role';
import { skillService } from './skillService';

const COLLECTION_NAME = 'roles';

export const roleService = {
  // Create a new role
  async createRole(role: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      console.log('roleService.createRole: Input role data:', role);
      console.log('roleService.createRole: Skills with certifications:', role.skills?.map(s => ({
        skillId: s.skillId,
        skillName: s.skillName,
        proficiency: s.proficiency,
        certifications: s.certifications,
        certificationsCount: s.certifications?.length || 0
      })));

      const roleData = {
        ...role,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      console.log('roleService.createRole: Final roleData before Firebase save:', roleData);

      // Deep clone to ensure we're not modifying the original
      const firebaseData = JSON.parse(JSON.stringify(roleData));
      console.log('roleService.createRole: Data being sent to Firebase (after JSON serialization):', firebaseData);

      const docRef = await addDoc(collection(db, COLLECTION_NAME), firebaseData);
      console.log('roleService.createRole: Successfully saved with ID:', docRef.id);

      // Immediately read back the saved data to verify
      const savedDoc = await getDoc(docRef);
      if (savedDoc.exists()) {
        const savedData = savedDoc.data();
        console.log('roleService.createRole: Data read back from Firebase:', savedData);
        console.log('roleService.createRole: Skills in saved data:', savedData.skills?.map(s => ({
          skillId: s.skillId,
          skillName: s.skillName,
          certifications: s.certifications,
          certificationsCount: s.certifications?.length || 0
        })));
      }

      return docRef.id;
    } catch (error) {
      console.error('Error creating role:', error);
      throw new Error('Failed to create role');
    }
  },

  // Get all roles
  async getAllRoles(): Promise<Role[]> {
    try {
      const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      const roles = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Role[];

      // Enrich roles with full skill details
      for (const role of roles) {
        if (role.skills) {
          for (let i = 0; i < role.skills.length; i++) {
            const roleSkill = role.skills[i];
            const skill = await skillService.getSkillById(roleSkill.skillId);
            if (skill) {
              role.skills[i].skillName = skill.name;
            }
          }
        }
      }
      return roles;
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw new Error('Failed to fetch roles');
    }
  },

  // Get a single role by ID
  async getRoleById(id: string): Promise<Role | null> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const role = docSnap.data() as Role;
        role.id = docSnap.id;

        // Enrich skills with names
        if (role.skills) {
          for (let i = 0; i < role.skills.length; i++) {
            const skillDetails = await skillService.getSkillById(role.skills[i].skillId);
            if (skillDetails) {
              role.skills[i].skillName = skillDetails.name;
            }
          }
        }
        return role;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching role:', error);
      throw new Error('Failed to fetch role');
    }
  },

  // Update a role
  async updateRole(id: string, updates: Partial<Omit<Role, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const updateData = {
        ...updates,
        updatedAt: Timestamp.now()
      };
      
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating role:', error);
      throw new Error('Failed to update role');
    }
  },

  // Delete a role
  async deleteRole(id: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting role:', error);
      throw new Error('Failed to delete role');
    }
  }
};
