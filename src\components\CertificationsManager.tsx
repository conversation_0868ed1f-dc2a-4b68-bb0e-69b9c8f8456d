import React, { useState } from 'react';
import { Plus, X, ExternalLink, Edit2 } from 'lucide-react';
import { Certification } from '../types/role';

interface CertificationsManagerProps {
  certifications: Certification[];
  onCertificationsChange: (certifications: Certification[]) => void;
  className?: string;
}

const CertificationsManager: React.FC<CertificationsManagerProps> = ({
  certifications,
  onCertificationsChange,
  className = ''
}) => {
  console.log('CertificationsManager: Rendered with certifications:', certifications);
  console.log('CertificationsManager: certifications length:', certifications?.length);
  console.log('CertificationsManager: onCertificationsChange function:', typeof onCertificationsChange);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    links: ['']
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      links: ['']
    });
    setShowAddForm(false);
    setEditingIndex(null);
  };

  const handleSubmit = (e?: React.FormEvent | React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    console.log('🎯 CertificationsManager: Form submitted with data', formData);
    alert('Form submitted! Check console for details.');

    // Validate required fields
    if (!formData.name.trim() || !formData.description.trim()) {
      console.log('CertificationsManager: Form validation failed - missing name or description');
      alert('Please fill in both name and description');
      return;
    }

    console.log('CertificationsManager: Form validation passed');

    const validLinks = formData.links.filter(link => link.trim() !== '');
    const newCertification: Certification = {
      id: editingIndex !== null ? certifications[editingIndex].id : Date.now().toString(),
      name: formData.name.trim(),
      description: formData.description.trim(),
      links: validLinks
    };

    let updatedCertifications: Certification[];
    if (editingIndex !== null) {
      updatedCertifications = [...certifications];
      updatedCertifications[editingIndex] = newCertification;
      console.log('CertificationsManager: Editing existing certification at index', editingIndex);
    } else {
      updatedCertifications = [...certifications, newCertification];
      console.log('CertificationsManager: Adding new certification');
    }

    console.log('CertificationsManager: Updated certifications array', updatedCertifications);

    // Call the parent callback immediately
    console.log('CertificationsManager: About to call onCertificationsChange with:', updatedCertifications);
    console.log('CertificationsManager: onCertificationsChange function:', onCertificationsChange);
    onCertificationsChange(updatedCertifications);
    console.log('CertificationsManager: onCertificationsChange called successfully');
    
    // Reset form after successful submission
    resetForm();
  };

  const handleEdit = (index: number) => {
    const cert = certifications[index];
    setFormData({
      name: cert.name,
      description: cert.description,
      links: cert.links.length > 0 ? cert.links : ['']
    });
    setEditingIndex(index);
    setShowAddForm(true);
  };

  const handleDelete = (index: number) => {
    const updatedCertifications = certifications.filter((_, i) => i !== index);
    onCertificationsChange(updatedCertifications);
  };

  const handleLinkChange = (index: number, value: string) => {
    const updatedLinks = [...formData.links];
    updatedLinks[index] = value;
    setFormData({ ...formData, links: updatedLinks });
  };

  const addLinkField = () => {
    setFormData({ ...formData, links: [...formData.links, ''] });
  };

  const removeLinkField = (index: number) => {
    const updatedLinks = formData.links.filter((_, i) => i !== index);
    setFormData({ ...formData, links: updatedLinks });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Certifications ({certifications.length})
        </label>
        {!showAddForm && (
          <button
            type="button"
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Certification
          </button>
        )}
      </div>

      {/* Existing Certifications */}
      {certifications.length > 0 && (
        <div className="space-y-3">
          {certifications.map((cert, index) => (
            <div key={cert.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{cert.name}</h4>
                  <p className="text-sm text-gray-600 mt-1">{cert.description}</p>
                  {cert.links.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {cert.links.map((link, linkIndex) => (
                        <a
                          key={linkIndex}
                          href={link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          {link}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    type="button"
                    onClick={() => handleEdit(index)}
                    className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                  >
                    <Edit2 className="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDelete(index)}
                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-300 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">
              {editingIndex !== null ? 'Edit Certification' : 'Add New Certification'}
            </h4>
            <button
              type="button"
              onClick={resetForm}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Certification Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., AWS Certified Solutions Architect"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe the certification and its relevance..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Links (Optional)
                </label>
                {formData.links.map((link, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <input
                      type="url"
                      value={link}
                      onChange={(e) => handleLinkChange(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://..."
                    />
                    {formData.links.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeLinkField(index)}
                        className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addLinkField}
                  className="inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Link
                </button>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
                >
                  {editingIndex !== null ? 'Update' : 'Add'} Certification
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CertificationsManager;