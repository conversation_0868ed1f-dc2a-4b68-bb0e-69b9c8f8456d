import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Settings, User, BookOpen, Users } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const navigationItems = [
    {
      name: 'Skills',
      path: '/skills',
      icon: BookOpen
    },
    {
      name: 'Roles',
      path: '/roles',
      icon: Users
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gradient-to-b from-blue-900 to-blue-800 shadow-lg flex flex-col">
        {/* Logo and Brand */}
        <div className="p-6 border-b border-blue-700 flex-shrink-0">
          <h1 className="text-2xl font-bold text-white">Skill Edge</h1>
          <p className="text-sm text-blue-200">Portal</p>
        </div>

        {/* Navigation */}
        <nav className="flex-1 mt-6 px-4">
          <ul className="space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);

              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                      active
                        ? 'bg-blue-700 text-white shadow-md'
                        : 'text-blue-200 hover:text-white hover:bg-blue-700'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* User Menu */}
        <div className="p-4 border-t border-blue-700 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <button className="p-2 text-blue-200 hover:text-white hover:bg-blue-700 rounded-lg transition-colors">
              <Settings className="h-5 w-5" />
            </button>
            <button className="p-2 text-blue-200 hover:text-white hover:bg-blue-700 rounded-lg transition-colors">
              <User className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 h-16">
          <div className="h-full px-6 flex items-center justify-between">
            <div className="text-lg font-semibold text-gray-900">
              {navigationItems.find(item => isActive(item.path))?.name || 'Dashboard'}
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome back!</span>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
